#!/bin/bash

# Certificate Scanner 增强版启动脚本
# 
# 使用方法:
#   ./run_java.sh          # 直接运行程序
#   ./run_java.sh build    # 重新编译并运行
#   ./run_java.sh clean    # 清理编译文件
#   ./run_java.sh test     # 测试API功能

echo "🔍 Certificate Scanner 增强版 v2.0.0"
echo "集成Cloudflare绕过功能"
echo "=================================="

case "$1" in
    "build")
        echo "正在编译项目..."
        mvn clean package
        if [ $? -eq 0 ]; then
            echo "✅ 编译成功，启动程序..."
            java -jar target/crt-scanner-2.0.0.jar
        else
            echo "❌ 编译失败！"
            exit 1
        fi
        ;;
    "clean")
        echo "清理编译文件..."
        mvn clean
        echo "✅ 清理完成！"
        ;;
    "test")
        echo "测试API功能..."
        if [ -f "target/classes/com/crtscanner/CloudflareBypassService.class" ]; then
            java -cp "target/classes:$(mvn dependency:build-classpath -q -Dmdep.outputFile=/dev/stdout)" TestCloudflareAPI
        else
            echo "⚠️ 需要先编译项目"
            mvn clean compile
            if [ $? -eq 0 ]; then
                java -cp "target/classes:$(mvn dependency:build-classpath -q -Dmdep.outputFile=/dev/stdout)" TestCloudflareAPI
            else
                echo "❌ 编译失败！"
                exit 1
            fi
        fi
        ;;
    *)
        echo "启动证书扫描工具增强版..."
        if [ -f "target/crt-scanner-2.0.0.jar" ]; then
            java -jar target/crt-scanner-2.0.0.jar
        else
            echo "⚠️ JAR文件不存在，正在编译..."
            mvn clean package
            if [ $? -eq 0 ]; then
                echo "✅ 编译成功，启动程序..."
                java -jar target/crt-scanner-2.0.0.jar
            else
                echo "❌ 编译失败！"
                exit 1
            fi
        fi
        ;;
esac

echo ""
echo "💡 提示:"
echo "  • 程序支持自动获取Censys Cookie"
echo "  • 启用'自动绕过Cloudflare保护'获得最佳体验"
echo "  • 使用'测试API'按钮验证功能"
