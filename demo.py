#!/usr/bin/env python3
"""
证书扫描工具演示脚本
简化版本，用于演示核心功能
"""

import re
import time
from urllib.parse import quote
from curl_cffi import requests
from bs4 import BeautifulSoup


def demo_crt_sh_query(domain):
    """演示crt.sh查询功能"""
    print(f"演示: 查询 {domain} 的证书信息")
    
    session = requests.Session(impersonate="chrome110")
    url = f"https://crt.sh/?q={quote(domain)}"
    
    try:
        print(f"请求URL: {url}")
        response = session.get(url, timeout=10)
        
        if response.status_code == 200:
            print("✓ crt.sh 请求成功")
            
            # 简单解析
            soup = BeautifulSoup(response.text, 'html.parser')
            cert_links = soup.find_all('a', href=True)
            
            cert_ids = []
            for link in cert_links:
                href = link.get('href')
                if href and '?id=' in href:
                    cert_id = href.split('?id=')[1]
                    if cert_id.isdigit():
                        cert_ids.append(cert_id)
            
            if cert_ids:
                print(f"✓ 找到 {len(cert_ids)} 个证书ID")
                print(f"  第一个ID: {cert_ids[0]}")
                print(f"  最后一个ID: {cert_ids[-1]}")
                return cert_ids[0]
            else:
                print("✗ 未找到证书ID")
                return None
        else:
            print(f"✗ 请求失败，状态码: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"✗ 请求异常: {e}")
        return None


def demo_cert_details(cert_id):
    """演示证书详情查询"""
    print(f"\n演示: 查询证书ID {cert_id} 的详情")
    
    session = requests.Session(impersonate="chrome110")
    url = f"https://crt.sh/?id={cert_id}"
    
    try:
        response = session.get(url, timeout=10)
        
        if response.status_code == 200:
            print("✓ 证书详情请求成功")
            
            # 查找SHA-256
            sha256_pattern = r'SHA-256.*?([a-fA-F0-9]{64})'
            match = re.search(sha256_pattern, response.text, re.IGNORECASE | re.DOTALL)
            
            if match:
                sha256 = match.group(1).upper()
                print(f"✓ 找到SHA-256指纹: {sha256[:16]}...")
                return sha256
            else:
                print("✗ 未找到SHA-256指纹")
                return None
        else:
            print(f"✗ 请求失败，状态码: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"✗ 请求异常: {e}")
        return None


def demo_censys_search(sha256):
    """演示Censys搜索（仅构造URL）"""
    print(f"\n演示: Censys搜索构造")
    
    search_query = f'cert.fingerprint_sha256="{sha256}"'
    url = f"https://platform.censys.io/search?q={quote(search_query)}"
    
    print(f"搜索查询: {search_query}")
    print(f"搜索URL: {url}")
    print("注意: Censys需要登录和cookie才能访问")
    
    return url


def main():
    print("=" * 60)
    print("证书扫描工具 - 功能演示")
    print("=" * 60)
    
    # 演示域名
    demo_domain = "example.com"
    
    # 步骤1: 查询crt.sh
    cert_id = demo_crt_sh_query(demo_domain)
    
    if cert_id:
        # 步骤2: 获取证书详情
        sha256 = demo_cert_details(cert_id)
        
        if sha256:
            # 步骤3: 构造Censys搜索
            censys_url = demo_censys_search(sha256)
            
            print("\n" + "=" * 60)
            print("演示完成!")
            print("=" * 60)
            print("完整流程:")
            print(f"1. 查询域名: {demo_domain}")
            print(f"2. 获取证书ID: {cert_id}")
            print(f"3. 提取SHA-256: {sha256[:16]}...")
            print(f"4. 构造搜索URL: {censys_url[:50]}...")
            print("\n要使用完整功能，请运行:")
            print(f"python3 cert_scanner.py {demo_domain} --cookie 'your_cookie'")
        else:
            print("\n演示中断: 无法获取证书详情")
    else:
        print("\n演示中断: 无法查询证书信息")


if __name__ == "__main__":
    main()
