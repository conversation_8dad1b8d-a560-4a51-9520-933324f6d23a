#!/usr/bin/env python3
"""
基本功能测试脚本
"""

import sys

def test_imports():
    """测试导入"""
    try:
        import curl_cffi
        print("✓ curl_cffi 导入成功")
    except ImportError as e:
        print(f"✗ curl_cffi 导入失败: {e}")
        return False
    
    try:
        from bs4 import BeautifulSoup
        print("✓ BeautifulSoup 导入成功")
    except ImportError as e:
        print(f"✗ BeautifulSoup 导入失败: {e}")
        return False
    
    try:
        import re, json, time, argparse, os
        from urllib.parse import urlparse, quote
        print("✓ 标准库导入成功")
    except ImportError as e:
        print(f"✗ 标准库导入失败: {e}")
        return False
    
    return True

def test_cert_scanner_import():
    """测试主模块导入"""
    try:
        from cert_scanner import CertScanner
        print("✓ CertScanner 类导入成功")
        return True
    except ImportError as e:
        print(f"✗ CertScanner 类导入失败: {e}")
        return False
    except Exception as e:
        print(f"✗ CertScanner 类导入出错: {e}")
        return False

def test_basic_functionality():
    """测试基本功能"""
    try:
        from cert_scanner import CertScanner
        scanner = CertScanner()
        
        # 测试域名提取
        domain = scanner.extract_domain("https://www.example.com/path")
        if domain == "www.example.com":
            print("✓ 域名提取功能正常")
        else:
            print(f"✗ 域名提取功能异常: {domain}")
            return False
        
        return True
    except Exception as e:
        print(f"✗ 基本功能测试失败: {e}")
        return False

def main():
    print("=" * 50)
    print("证书扫描工具 - 基本功能测试")
    print("=" * 50)
    
    tests = [
        ("导入测试", test_imports),
        ("主模块导入测试", test_cert_scanner_import),
        ("基本功能测试", test_basic_functionality),
    ]
    
    passed = 0
    total = len(tests)
    
    for name, test_func in tests:
        print(f"\n{name}:")
        if test_func():
            passed += 1
            print(f"✓ {name} 通过")
        else:
            print(f"✗ {name} 失败")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    print("=" * 50)
    
    if passed == total:
        print("✓ 所有测试通过，工具可以正常使用")
        return 0
    else:
        print("✗ 部分测试失败，请检查环境配置")
        return 1

if __name__ == "__main__":
    sys.exit(main())
