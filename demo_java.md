# Certificate Scanner Java增强版 - 功能演示

## 🎯 项目概述

Certificate Scanner Java增强版是一个功能强大的证书扫描工具，集成了Cloudflare绕过功能，可以自动化获取网站证书信息并查询使用相同证书的其他网站。

## 🆕 增强版新功能

### 1. 自动Cloudflare绕过
- **API集成**: 使用第三方API自动获取Cookie
- **一键操作**: 点击"自动获取Cookie"按钮即可
- **智能检测**: 自动检测Cloudflare拦截并重试
- **多域名支持**: 支持获取不同域名的Cookie

### 2. 增强的用户界面
- **现代化设计**: 彩色按钮和状态指示
- **实时反馈**: 详细的操作状态显示
- **智能提示**: 用户友好的错误提示和建议
- **响应式布局**: 适应不同屏幕尺寸

### 3. 智能错误处理
- **自动重试**: 遇到错误时自动重试
- **错误恢复**: 智能恢复机制
- **详细日志**: 完整的操作日志记录
- **用户指导**: 提供解决方案建议

## 🚀 使用流程演示

### 步骤1: 启动程序
```bash
# 方法1: 直接运行
java -jar target/crt-scanner-2.0.0.jar

# 方法2: 使用脚本
./run_java.sh

# 方法3: 重新编译并运行
./run_java.sh build
```

### 步骤2: 测试API连接
1. 点击"测试API"按钮
2. 程序会验证Cloudflare绕过API是否可用
3. 显示测试结果

### 步骤3: 自动获取Cookie
1. 点击"自动获取Cookie"按钮
2. 程序自动从API获取Censys平台Cookie
3. Cookie会自动填充到界面中

### 步骤4: 配置扫描选项
1. 输入目标域名（如：example.com）
2. 确保"自动绕过Cloudflare保护"选项已勾选
3. 可选：手动输入或修改Cookie

### 步骤5: 开始扫描
1. 点击"开始扫描"按钮
2. 程序会显示详细的扫描过程：
   - 查询证书信息
   - 获取证书详细信息
   - 提取SHA-256指纹
   - 搜索使用相同证书的网站

## 📊 输出示例

```
=== 证书扫描开始 (增强版) ===
目标域名: example.com
自动Cloudflare绕过: 启用

步骤 1: 查询证书信息...
🔍 正在查询域名 example.com 的证书信息...
✅ 通过JSON API找到 2 个证书

步骤 2.1: 获取证书详细信息 (ID: 12345678)...
📋 正在获取证书 12345678 的详细信息...
✅ 成功获取证书详情，SHA-256: A1B2C3D4E5F6...
证书ID: 12345678
通用名称: *.example.com
SHA-256指纹: A1:B2:C3:D4:E5:F6:07:08:09:0A:1B:2C:3D:4E:5F:60:71:82:93:A4:B5:C6:D7:E8:F9:0A:1B:2C:3D:4E:5F:60
颁发者: CN=DigiCert TLS RSA SHA256 2020 CA1, O=DigiCert Inc, C=US
有效期: 2024-01-01 00:00:00 至 2025-01-01 00:00:00

步骤 3.1: 查询使用相同证书的网站...
🌐 正在Censys平台搜索使用证书指纹 A1B2C3D4E5F6... 的域名
📡 Censys响应状态码: 200
✅ 找到 15 个使用相同证书的域名/IP
  - example.com
  - www.example.com
  - api.example.com
  - cdn.example.com
  - mail.example.com
  ...

=== 扫描完成 ===
```

## 🔧 技术特点

### API集成
- **端点**: `http://api-cf.zjdanli.com/cloudflare/getCookie`
- **认证**: 使用预配置的APP ID
- **请求格式**: JSON POST请求
- **响应处理**: 智能解析和错误处理

### 网络请求优化
- **用户代理**: 模拟真实浏览器
- **请求头**: 完整的HTTP头信息
- **超时控制**: 合理的超时设置
- **连接复用**: 高效的连接管理

### 数据解析
- **多格式支持**: JSON和HTML双重解析
- **容错机制**: 处理各种异常情况
- **数据清洗**: 智能过滤和验证
- **结果优化**: 去重和排序

## 🛡️ 安全特性

### 隐私保护
- **本地处理**: Cookie仅在本地使用
- **不存储敏感信息**: 不保存用户数据
- **安全传输**: HTTPS加密通信
- **最小权限**: 仅请求必要的权限

### 错误处理
- **异常捕获**: 全面的异常处理
- **安全降级**: 失败时的安全回退
- **日志记录**: 详细的操作日志
- **用户提示**: 清晰的错误信息

## 📈 性能优化

### 响应速度
- **并发处理**: 多线程操作
- **缓存机制**: 智能缓存策略
- **请求优化**: 减少不必要的请求
- **界面响应**: 非阻塞UI操作

### 资源管理
- **内存优化**: 高效的内存使用
- **连接管理**: 自动连接清理
- **线程池**: 合理的线程管理
- **垃圾回收**: 优化的对象生命周期

## 🎯 使用场景

### 安全研究
- 证书透明度分析
- 域名资产发现
- SSL/TLS配置审计
- 安全漏洞评估

### 运维监控
- 证书到期监控
- 域名配置检查
- 基础设施映射
- 合规性检查

### 渗透测试
- 目标侦察
- 攻击面分析
- 关联域名发现
- 证书链分析

## 🔮 未来规划

### 功能扩展
- [ ] 支持更多证书查询源
- [ ] 添加证书链分析
- [ ] 集成更多安全检查
- [ ] 支持批量扫描

### 界面改进
- [ ] 深色主题支持
- [ ] 自定义界面布局
- [ ] 结果导出功能
- [ ] 历史记录管理

### 性能提升
- [ ] 异步处理优化
- [ ] 缓存策略改进
- [ ] 网络请求优化
- [ ] 内存使用优化

---

## 🎉 总结

Certificate Scanner Java增强版通过集成Cloudflare绕过API，实现了真正的自动化证书扫描体验。用户无需手动获取Cookie，只需点击几个按钮即可完成完整的扫描流程。

**主要优势：**
- 🤖 **全自动化**: 一键获取Cookie，无需手动操作
- 🛡️ **绕过保护**: 有效绕过Cloudflare等反爬虫保护
- 🖥️ **用户友好**: 直观的图形界面和清晰的操作流程
- ⚡ **高效稳定**: 智能重试和错误恢复机制
- 🔒 **安全可靠**: 本地处理，保护用户隐私

这个增强版本将证书扫描的门槛降到了最低，让任何用户都能轻松使用这个强大的工具进行安全研究和运维监控。
