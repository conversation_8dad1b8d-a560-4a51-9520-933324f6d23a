package com.crtscanner;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Service for certificate operations
 */
public class CertificateService {
    private static final String CRT_SH_SEARCH_URL = "https://crt.sh/?q=";
    private static final String CRT_SH_DETAIL_URL = "https://crt.sh/?id=";
    private static final String CENSYS_SEARCH_URL = "https://search.censys.io/search?resource=hosts&sort=RELEVANCE&per_page=25&virtual_hosts=EXCLUDE&q=";
    
    private final CloseableHttpClient httpClient;
    private final ObjectMapper objectMapper;
    
    public CertificateService() {
        this.httpClient = HttpClients.createDefault();
        this.objectMapper = new ObjectMapper();
    }
    
    /**
     * Search certificates for a domain
     */
    public List<CertificateInfo> searchCertificates(String domain) throws IOException {
        String encodedDomain;
        try {
            encodedDomain = URLEncoder.encode(domain, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            throw new IOException("UTF-8 encoding not supported", e);
        }
        String url = CRT_SH_SEARCH_URL + encodedDomain + "&output=json";
        
        HttpGet request = new HttpGet(url);
        request.setHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");
        
        try (CloseableHttpResponse response = httpClient.execute(request)) {
            HttpEntity entity = response.getEntity();
            String jsonResponse = EntityUtils.toString(entity);
            
            JsonNode rootNode = objectMapper.readTree(jsonResponse);
            List<CertificateInfo> certificates = new ArrayList<>();
            
            if (rootNode.isArray() && rootNode.size() > 0) {
                // Get first and last certificate
                JsonNode firstCert = rootNode.get(0);
                JsonNode lastCert = rootNode.get(rootNode.size() - 1);
                
                certificates.add(createCertificateInfo(firstCert));
                if (rootNode.size() > 1) {
                    certificates.add(createCertificateInfo(lastCert));
                }
            }
            
            return certificates;
        }
    }
    
    /**
     * Get certificate details by ID
     */
    public CertificateInfo getCertificateDetails(String certId) throws IOException {
        String url = CRT_SH_DETAIL_URL + certId;
        
        HttpGet request = new HttpGet(url);
        request.setHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");
        
        try (CloseableHttpResponse response = httpClient.execute(request)) {
            HttpEntity entity = response.getEntity();
            String htmlResponse = EntityUtils.toString(entity);
            
            return parseCertificateDetails(htmlResponse, certId);
        }
    }
    
    /**
     * Parse certificate details from HTML
     */
    private CertificateInfo parseCertificateDetails(String html, String certId) {
        Document doc = Jsoup.parse(html);
        CertificateInfo certInfo = new CertificateInfo();
        certInfo.setId(certId);
        
        // Find SHA-256 fingerprint
        Elements rows = doc.select("tr");
        for (Element row : rows) {
            Elements cells = row.select("td");
            if (cells.size() >= 2) {
                String label = cells.get(0).text().trim();
                String value = cells.get(1).text().trim();
                
                if (label.contains("SHA-256")) {
                    certInfo.setSha256Fingerprint(value);
                } else if (label.contains("Subject")) {
                    // Extract CN from subject
                    Pattern cnPattern = Pattern.compile("CN=([^,]+)");
                    Matcher matcher = cnPattern.matcher(value);
                    if (matcher.find()) {
                        certInfo.setCommonName(matcher.group(1));
                    }
                } else if (label.contains("Issuer")) {
                    certInfo.setIssuer(value);
                } else if (label.contains("Not Before")) {
                    certInfo.setValidFrom(value);
                } else if (label.contains("Not After")) {
                    certInfo.setValidTo(value);
                }
            }
        }
        
        return certInfo;
    }
    
    /**
     * Create certificate info from JSON node
     */
    private CertificateInfo createCertificateInfo(JsonNode certNode) {
        CertificateInfo cert = new CertificateInfo();
        cert.setId(certNode.get("id").asText());
        cert.setCommonName(certNode.get("common_name").asText());
        return cert;
    }
    
    /**
     * Close HTTP client
     */
    public void close() throws IOException {
        if (httpClient != null) {
            httpClient.close();
        }
    }
}
