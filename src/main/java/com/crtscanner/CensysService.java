package com.crtscanner;

import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Service for Censys platform operations
 */
public class CensysService {
    private static final String CENSYS_SEARCH_URL = "https://search.censys.io/search?resource=hosts&sort=RELEVANCE&per_page=25&virtual_hosts=EXCLUDE&q=";
    
    private final CloseableHttpClient httpClient;
    private String cookieHeader;
    
    public CensysService() {
        this.httpClient = HttpClients.createDefault();
    }
    
    /**
     * Set cookie for authentication
     */
    public void setCookie(String cookie) {
        this.cookieHeader = cookie;
    }
    
    /**
     * Search domains using the same certificate fingerprint
     */
    public List<String> searchDomainsByCertFingerprint(String sha256Fingerprint) throws IOException {
        if (cookieHeader == null || cookieHeader.trim().isEmpty()) {
            throw new IllegalStateException("Cookie is required for Censys search. Please set cookie first.");
        }
        
        String query = "services.tls.certificates.leaf_data.fingerprint_sha256:" + sha256Fingerprint;
        String encodedQuery;
        try {
            encodedQuery = URLEncoder.encode(query, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            throw new IOException("UTF-8 encoding not supported", e);
        }
        String url = CENSYS_SEARCH_URL + encodedQuery;
        
        HttpGet request = new HttpGet(url);
        request.setHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36");
        request.setHeader("Cookie", cookieHeader);
        request.setHeader("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8");
        request.setHeader("Accept-Language", "en-US,en;q=0.5");
        request.setHeader("Accept-Encoding", "gzip, deflate, br");
        request.setHeader("DNT", "1");
        request.setHeader("Connection", "keep-alive");
        request.setHeader("Upgrade-Insecure-Requests", "1");
        
        try (CloseableHttpResponse response = httpClient.execute(request)) {
            int statusCode = response.getStatusLine().getStatusCode();
            
            if (statusCode == 403) {
                throw new IOException("Access denied. Please check your cookie or handle Cloudflare protection.");
            } else if (statusCode == 401) {
                throw new IOException("Authentication failed. Please check your cookie.");
            } else if (statusCode != 200) {
                throw new IOException("HTTP request failed with status: " + statusCode);
            }
            
            HttpEntity entity = response.getEntity();
            String htmlResponse = EntityUtils.toString(entity);
            
            return parseDomainNames(htmlResponse);
        }
    }
    
    /**
     * Parse domain names from Censys search results
     */
    private List<String> parseDomainNames(String html) {
        Set<String> domainSet = new HashSet<>();
        Document doc = Jsoup.parse(html);
        
        // Method 1: Look for IP addresses and associated domains in search results
        Elements resultItems = doc.select("[data-testid='search-result-item']");
        for (Element item : resultItems) {
            // Extract IP address
            Elements ipElements = item.select("a[href*='/hosts/']");
            for (Element ipElement : ipElements) {
                String href = ipElement.attr("href");
                Pattern ipPattern = Pattern.compile("/hosts/([0-9.]+)");
                Matcher matcher = ipPattern.matcher(href);
                if (matcher.find()) {
                    String ip = matcher.group(1);
                    domainSet.add(ip);
                }
            }
            
            // Extract domain names from text content
            String itemText = item.text();
            extractDomainsFromText(itemText, domainSet);
        }
        
        // Method 2: Look for certificate subject alternative names
        Elements certElements = doc.select("*:contains('DNS:')");
        for (Element element : certElements) {
            String text = element.text();
            extractDomainsFromText(text, domainSet);
        }
        
        // Method 3: Look for any domain-like patterns in the page
        String pageText = doc.text();
        extractDomainsFromText(pageText, domainSet);
        
        return new ArrayList<>(domainSet);
    }
    
    /**
     * Extract domain names from text using regex
     */
    private void extractDomainsFromText(String text, Set<String> domainSet) {
        // Pattern for domain names
        Pattern domainPattern = Pattern.compile("(?:^|\\s|DNS:)([a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*)(?=\\s|$|,|;)");
        Matcher matcher = domainPattern.matcher(text);
        
        while (matcher.find()) {
            String domain = matcher.group(1);
            if (domain != null && domain.contains(".") && !domain.startsWith(".") && !domain.endsWith(".")) {
                // Filter out common false positives
                if (!domain.matches(".*\\.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$") &&
                    !domain.matches("^\\d+\\.\\d+\\.\\d+\\.\\d+$") && // Not an IP address
                    domain.length() > 3 && domain.length() < 100) {
                    domainSet.add(domain.toLowerCase());
                }
            }
        }
        
        // Pattern for wildcard domains
        Pattern wildcardPattern = Pattern.compile("\\*\\.([a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*)");
        Matcher wildcardMatcher = wildcardPattern.matcher(text);
        
        while (wildcardMatcher.find()) {
            String domain = wildcardMatcher.group(1);
            if (domain != null && domain.contains(".")) {
                domainSet.add("*." + domain.toLowerCase());
            }
        }
    }
    
    /**
     * Close HTTP client
     */
    public void close() throws IOException {
        if (httpClient != null) {
            httpClient.close();
        }
    }
}
