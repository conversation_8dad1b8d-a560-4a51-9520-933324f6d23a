package com.crtscanner;

import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Service for Censys platform operations with Cloudflare bypass
 */
public class CensysService {
    private static final String CENSYS_SEARCH_URL = "https://search.censys.io/search?resource=hosts&sort=RELEVANCE&per_page=25&virtual_hosts=EXCLUDE&q=";
    
    private final CloseableHttpClient httpClient;
    private String cookieHeader;
    private CloudflareBypassService cloudflareService;
    private boolean autoBypassCloudflare;
    
    public CensysService() {
        this.httpClient = HttpClients.createDefault();
        this.cloudflareService = new CloudflareBypassService();
        this.autoBypassCloudflare = true;
    }
    
    /**
     * Set cookie for authentication
     */
    public void setCookie(String cookie) {
        this.cookieHeader = cookie;
        System.out.println("✅ 已设置Censys Cookie");
    }
    
    /**
     * Enable or disable automatic Cloudflare bypass
     */
    public void setAutoBypassCloudflare(boolean enable) {
        this.autoBypassCloudflare = enable;
        System.out.println(enable ? "✅ 已启用自动Cloudflare绕过" : "⚠️ 已禁用自动Cloudflare绕过");
    }
    
    /**
     * Automatically get Censys cookie using Cloudflare bypass API
     */
    public boolean autoGetCensysCookie() {
        if (!autoBypassCloudflare) {
            System.out.println("⚠️ 自动Cloudflare绕过已禁用");
            return false;
        }
        
        System.out.println("🔄 正在自动获取Censys Cookie...");
        String cookie = cloudflareService.getCensysCookie();
        
        if (cookie != null && !cookie.isEmpty()) {
            setCookie(cookie);
            return true;
        } else {
            System.err.println("❌ 自动获取Censys Cookie失败");
            return false;
        }
    }
    
    /**
     * Search domains using the same certificate fingerprint
     */
    public List<String> searchDomainsByCertFingerprint(String sha256Fingerprint) throws IOException {
        // 如果没有Cookie且启用了自动绕过，尝试自动获取
        if ((cookieHeader == null || cookieHeader.trim().isEmpty()) && autoBypassCloudflare) {
            System.out.println("🔄 检测到未设置Cookie，尝试自动获取...");
            if (!autoGetCensysCookie()) {
                throw new IllegalStateException("无法获取Censys Cookie，请手动设置或检查网络连接");
            }
        }
        
        if (cookieHeader == null || cookieHeader.trim().isEmpty()) {
            throw new IllegalStateException("Cookie is required for Censys search. Please set cookie first.");
        }
        
        System.out.println("🌐 正在Censys平台搜索使用证书指纹 " + sha256Fingerprint.substring(0, 32) + "... 的域名");
        
        String query = "services.tls.certificates.leaf_data.fingerprint_sha256:" + sha256Fingerprint;
        String encodedQuery;
        try {
            encodedQuery = URLEncoder.encode(query, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            throw new IOException("UTF-8 encoding not supported", e);
        }
        String url = CENSYS_SEARCH_URL + encodedQuery;
        
        HttpGet request = new HttpGet(url);
        request.setHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36");
        request.setHeader("Cookie", cookieHeader);
        request.setHeader("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8");
        request.setHeader("Accept-Language", "en-US,en;q=0.5");
        request.setHeader("Accept-Encoding", "gzip, deflate, br");
        request.setHeader("DNT", "1");
        request.setHeader("Connection", "keep-alive");
        request.setHeader("Upgrade-Insecure-Requests", "1");
        
        try (CloseableHttpResponse response = httpClient.execute(request)) {
            int statusCode = response.getStatusLine().getStatusCode();
            
            System.out.println("📡 Censys响应状态码: " + statusCode);
            
            if (statusCode == 403) {
                System.err.println("❌ 访问被拒绝，可能遇到Cloudflare保护");
                
                // 如果启用了自动绕过，尝试重新获取Cookie
                if (autoBypassCloudflare) {
                    System.out.println("🔄 尝试重新获取Cookie...");
                    if (autoGetCensysCookie()) {
                        // 递归调用，但要防止无限循环
                        return searchDomainsByCertFingerprintWithRetry(sha256Fingerprint, 1);
                    }
                }
                
                throw new IOException("Access denied. Please check your cookie or handle Cloudflare protection.");
            } else if (statusCode == 401) {
                throw new IOException("Authentication failed. Please check your cookie.");
            } else if (statusCode != 200) {
                throw new IOException("HTTP request failed with status: " + statusCode);
            }
            
            HttpEntity entity = response.getEntity();
            String htmlResponse = EntityUtils.toString(entity);
            
            // 检查是否被Cloudflare拦截
            if (isCloudflareBlocked(htmlResponse)) {
                System.err.println("❌ 被Cloudflare拦截");
                
                if (autoBypassCloudflare) {
                    System.out.println("🔄 尝试绕过Cloudflare保护...");
                    if (autoGetCensysCookie()) {
                        return searchDomainsByCertFingerprintWithRetry(sha256Fingerprint, 1);
                    }
                }
                
                throw new IOException("Blocked by Cloudflare. Please try updating cookie or wait and retry.");
            }
            
            List<String> domains = parseDomainNames(htmlResponse);
            
            if (!domains.isEmpty()) {
                System.out.println("✅ 找到 " + domains.size() + " 个使用相同证书的域名/IP");
            } else {
                System.out.println("❌ 未找到使用相同证书的其他域名");
            }
            
            return domains;
        }
    }
    
    /**
     * Search with retry mechanism
     */
    private List<String> searchDomainsByCertFingerprintWithRetry(String sha256Fingerprint, int retryCount) throws IOException {
        if (retryCount > 2) {
            throw new IOException("Maximum retry attempts reached");
        }
        
        try {
            // 等待一段时间再重试
            Thread.sleep(2000);
            return searchDomainsByCertFingerprint(sha256Fingerprint);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new IOException("Retry interrupted", e);
        }
    }
    
    /**
     * Check if response is blocked by Cloudflare
     */
    private boolean isCloudflareBlocked(String htmlContent) {
        String[] cloudflareIndicators = {
            "Checking your browser before accessing",
            "DDoS protection by Cloudflare",
            "cf-browser-verification",
            "cf-challenge-form",
            "Just a moment",
            "Please wait while your request is being verified",
            "Ray ID:",
            "cloudflare"
        };
        
        String lowerContent = htmlContent.toLowerCase();
        for (String indicator : cloudflareIndicators) {
            if (lowerContent.contains(indicator.toLowerCase())) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Parse domain names from Censys search results
     */
    private List<String> parseDomainNames(String html) {
        Set<String> domainSet = new HashSet<>();
        Document doc = Jsoup.parse(html);
        
        // Method 1: Look for IP addresses and associated domains in search results
        Elements resultItems = doc.select("[data-testid='search-result-item'], .result-item, .search-result");
        for (Element item : resultItems) {
            // Extract IP address
            Elements ipElements = item.select("a[href*='/hosts/']");
            for (Element ipElement : ipElements) {
                String href = ipElement.attr("href");
                Pattern ipPattern = Pattern.compile("/hosts/([0-9.]+)");
                Matcher matcher = ipPattern.matcher(href);
                if (matcher.find()) {
                    String ip = matcher.group(1);
                    domainSet.add(ip);
                }
            }
            
            // Extract domain names from text content
            String itemText = item.text();
            extractDomainsFromText(itemText, domainSet);
        }
        
        // Method 2: Look for certificate subject alternative names
        Elements certElements = doc.select("*:contains('DNS:')");
        for (Element element : certElements) {
            String text = element.text();
            extractDomainsFromText(text, domainSet);
        }
        
        // Method 3: Look for any domain-like patterns in the page
        String pageText = doc.text();
        extractDomainsFromText(pageText, domainSet);
        
        return new ArrayList<>(domainSet);
    }
    
    /**
     * Extract domain names from text using regex
     */
    private void extractDomainsFromText(String text, Set<String> domainSet) {
        // Pattern for domain names
        Pattern domainPattern = Pattern.compile("(?:^|\\s|DNS:)([a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*)(?=\\s|$|,|;)");
        Matcher matcher = domainPattern.matcher(text);
        
        while (matcher.find()) {
            String domain = matcher.group(1);
            if (domain != null && domain.contains(".") && !domain.startsWith(".") && !domain.endsWith(".")) {
                // Filter out common false positives
                if (!domain.matches(".*\\.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$") &&
                    !domain.matches("^\\d+\\.\\d+\\.\\d+\\.\\d+$") && // Not an IP address
                    domain.length() > 3 && domain.length() < 100) {
                    domainSet.add(domain.toLowerCase());
                }
            }
        }
        
        // Pattern for wildcard domains
        Pattern wildcardPattern = Pattern.compile("\\*\\.([a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*)");
        Matcher wildcardMatcher = wildcardPattern.matcher(text);
        
        while (wildcardMatcher.find()) {
            String domain = wildcardMatcher.group(1);
            if (domain != null && domain.contains(".")) {
                domainSet.add("*." + domain.toLowerCase());
            }
        }
    }
    
    /**
     * Test Cloudflare bypass functionality
     */
    public boolean testCloudflareBypass() {
        return cloudflareService.testConnection();
    }
    
    /**
     * Close HTTP client and Cloudflare service
     */
    public void close() throws IOException {
        if (httpClient != null) {
            httpClient.close();
        }
        if (cloudflareService != null) {
            cloudflareService.close();
        }
    }
}
