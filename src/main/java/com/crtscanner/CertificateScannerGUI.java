package com.crtscanner;

import javax.swing.*;
import javax.swing.border.TitledBorder;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.io.IOException;
import java.util.List;

/**
 * Main GUI application for Certificate Scanner with Cloudflare bypass
 */
public class CertificateScannerGUI extends J<PERSON>rame {
    private JTextField domainField;
    private JTextArea cookieArea;
    private JTextArea resultArea;
    private JButton scanButton;
    private JButton clearButton;
    private JButton autoGetCookieButton;
    private JButton testApiButton;
    private JCheckBox autoBypassCheckBox;
    private JProgressBar progressBar;
    private JLabel statusLabel;
    
    private CertificateService certificateService;
    private CensysService censysService;
    
    public CertificateScannerGUI() {
        this.certificateService = new CertificateService();
        this.censysService = new CensysService();
        
        initializeGUI();
        setupEventHandlers();
    }
    
    private void initializeGUI() {
        setTitle("Certificate Scanner - 证书扫描工具 (增强版)");
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        setLayout(new BorderLayout());
        
        // Create main panel
        JPanel mainPanel = new JPanel(new BorderLayout(10, 10));
        mainPanel.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));
        
        // Input panel
        JPanel inputPanel = createInputPanel();
        mainPanel.add(inputPanel, BorderLayout.NORTH);
        
        // Result panel
        JPanel resultPanel = createResultPanel();
        mainPanel.add(resultPanel, BorderLayout.CENTER);
        
        // Status panel
        JPanel statusPanel = createStatusPanel();
        mainPanel.add(statusPanel, BorderLayout.SOUTH);
        
        add(mainPanel);
        
        // Set window properties
        setSize(900, 800);
        setLocationRelativeTo(null);
        setMinimumSize(new Dimension(700, 600));
    }
    
    private JPanel createInputPanel() {
        JPanel panel = new JPanel(new GridBagLayout());
        panel.setBorder(new TitledBorder("输入配置"));
        GridBagConstraints gbc = new GridBagConstraints();
        
        // Domain input
        gbc.gridx = 0; gbc.gridy = 0;
        gbc.anchor = GridBagConstraints.WEST;
        gbc.insets = new Insets(5, 5, 5, 5);
        panel.add(new JLabel("目标域名:"), gbc);
        
        gbc.gridx = 1; gbc.gridy = 0;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        gbc.weightx = 1.0;
        domainField = new JTextField(20);
        domainField.setToolTipText("请输入要查询的域名，例如: example.com");
        panel.add(domainField, gbc);
        
        // Auto bypass checkbox
        gbc.gridx = 0; gbc.gridy = 1;
        gbc.gridwidth = 2;
        gbc.fill = GridBagConstraints.NONE;
        gbc.weightx = 0;
        autoBypassCheckBox = new JCheckBox("自动绕过Cloudflare保护", true);
        autoBypassCheckBox.setToolTipText("启用后将自动获取Cookie绕过Cloudflare保护");
        panel.add(autoBypassCheckBox, gbc);
        
        // Cookie input
        gbc.gridx = 0; gbc.gridy = 2;
        gbc.gridwidth = 1;
        gbc.anchor = GridBagConstraints.NORTHWEST;
        gbc.fill = GridBagConstraints.NONE;
        panel.add(new JLabel("Censys Cookie:"), gbc);
        
        gbc.gridx = 1; gbc.gridy = 2;
        gbc.fill = GridBagConstraints.BOTH;
        gbc.weightx = 1.0;
        gbc.weighty = 0.3;
        cookieArea = new JTextArea(3, 20);
        cookieArea.setLineWrap(true);
        cookieArea.setWrapStyleWord(true);
        cookieArea.setToolTipText("Cookie将自动获取，也可手动输入");
        JScrollPane cookieScroll = new JScrollPane(cookieArea);
        panel.add(cookieScroll, gbc);
        
        // Buttons
        gbc.gridx = 0; gbc.gridy = 3;
        gbc.gridwidth = 2;
        gbc.fill = GridBagConstraints.NONE;
        gbc.anchor = GridBagConstraints.CENTER;
        gbc.weighty = 0;
        
        JPanel buttonPanel = new JPanel(new FlowLayout());
        
        scanButton = new JButton("开始扫描");
        scanButton.setPreferredSize(new Dimension(100, 30));
        scanButton.setBackground(new Color(0, 123, 255));
        scanButton.setForeground(Color.WHITE);
        
        clearButton = new JButton("清空结果");
        clearButton.setPreferredSize(new Dimension(100, 30));
        
        autoGetCookieButton = new JButton("自动获取Cookie");
        autoGetCookieButton.setPreferredSize(new Dimension(120, 30));
        autoGetCookieButton.setBackground(new Color(40, 167, 69));
        autoGetCookieButton.setForeground(Color.WHITE);
        
        testApiButton = new JButton("测试API");
        testApiButton.setPreferredSize(new Dimension(80, 30));
        testApiButton.setBackground(new Color(255, 193, 7));
        testApiButton.setForeground(Color.BLACK);
        
        buttonPanel.add(scanButton);
        buttonPanel.add(clearButton);
        buttonPanel.add(autoGetCookieButton);
        buttonPanel.add(testApiButton);
        panel.add(buttonPanel, gbc);
        
        return panel;
    }
    
    private JPanel createResultPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBorder(new TitledBorder("扫描结果"));
        
        resultArea = new JTextArea();
        resultArea.setEditable(false);
        resultArea.setFont(new Font(Font.MONOSPACED, Font.PLAIN, 12));
        resultArea.setBackground(Color.WHITE);
        
        JScrollPane scrollPane = new JScrollPane(resultArea);
        scrollPane.setVerticalScrollBarPolicy(JScrollPane.VERTICAL_SCROLLBAR_ALWAYS);
        scrollPane.setHorizontalScrollBarPolicy(JScrollPane.HORIZONTAL_SCROLLBAR_AS_NEEDED);
        
        panel.add(scrollPane, BorderLayout.CENTER);
        
        return panel;
    }
    
    private JPanel createStatusPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        
        statusLabel = new JLabel("就绪 - 增强版支持自动Cloudflare绕过");
        statusLabel.setBorder(BorderFactory.createEmptyBorder(5, 5, 5, 5));
        
        progressBar = new JProgressBar();
        progressBar.setStringPainted(true);
        progressBar.setVisible(false);
        
        panel.add(statusLabel, BorderLayout.WEST);
        panel.add(progressBar, BorderLayout.CENTER);
        
        return panel;
    }
    
    private void setupEventHandlers() {
        scanButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                startScan();
            }
        });
        
        clearButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                resultArea.setText("");
                statusLabel.setText("就绪");
            }
        });
        
        autoGetCookieButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                autoGetCookie();
            }
        });
        
        testApiButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                testApi();
            }
        });
        
        autoBypassCheckBox.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                censysService.setAutoBypassCloudflare(autoBypassCheckBox.isSelected());
            }
        });
        
        // Enter key in domain field triggers scan
        domainField.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                startScan();
            }
        });
    }
    
    private void startScan() {
        String domain = domainField.getText().trim();
        if (domain.isEmpty()) {
            JOptionPane.showMessageDialog(this, "请输入域名", "错误", JOptionPane.ERROR_MESSAGE);
            return;
        }
        
        // Set cookie if provided
        String cookie = cookieArea.getText().trim();
        if (!cookie.isEmpty()) {
            censysService.setCookie(cookie);
        }
        
        // Set auto bypass option
        censysService.setAutoBypassCloudflare(autoBypassCheckBox.isSelected());
        
        // Start scanning in background thread
        new ScanWorker(domain).execute();
    }
    
    private void autoGetCookie() {
        new Thread(() -> {
            SwingUtilities.invokeLater(() -> {
                autoGetCookieButton.setEnabled(false);
                autoGetCookieButton.setText("获取中...");
                statusLabel.setText("正在自动获取Cookie...");
            });
            
            boolean success = censysService.autoGetCensysCookie();
            
            SwingUtilities.invokeLater(() -> {
                autoGetCookieButton.setEnabled(true);
                autoGetCookieButton.setText("自动获取Cookie");
                
                if (success) {
                    statusLabel.setText("Cookie获取成功");
                    JOptionPane.showMessageDialog(this, "Cookie获取成功！", "成功", JOptionPane.INFORMATION_MESSAGE);
                } else {
                    statusLabel.setText("Cookie获取失败");
                    JOptionPane.showMessageDialog(this, "Cookie获取失败，请检查网络连接", "错误", JOptionPane.ERROR_MESSAGE);
                }
            });
        }).start();
    }
    
    private void testApi() {
        new Thread(() -> {
            SwingUtilities.invokeLater(() -> {
                testApiButton.setEnabled(false);
                testApiButton.setText("测试中...");
                statusLabel.setText("正在测试API连接...");
            });
            
            boolean success = censysService.testCloudflareBypass();
            
            SwingUtilities.invokeLater(() -> {
                testApiButton.setEnabled(true);
                testApiButton.setText("测试API");
                
                if (success) {
                    statusLabel.setText("API连接正常");
                    JOptionPane.showMessageDialog(this, "API连接测试成功！", "成功", JOptionPane.INFORMATION_MESSAGE);
                } else {
                    statusLabel.setText("API连接失败");
                    JOptionPane.showMessageDialog(this, "API连接测试失败，请检查网络", "错误", JOptionPane.ERROR_MESSAGE);
                }
            });
        }).start();
    }

    /**
     * Background worker for scanning operations
     */
    private class ScanWorker extends SwingWorker<Void, String> {
        private final String domain;

        public ScanWorker(String domain) {
            this.domain = domain;
        }

        @Override
        protected void process(List<String> chunks) {
            for (String message : chunks) {
                resultArea.append(message + "\n");
                resultArea.setCaretPosition(resultArea.getDocument().getLength());
            }
        }

        @Override
        protected Void doInBackground() throws Exception {
            SwingUtilities.invokeLater(() -> {
                scanButton.setEnabled(false);
                progressBar.setVisible(true);
                progressBar.setIndeterminate(true);
                statusLabel.setText("正在扫描...");
                resultArea.setText("");
            });

            try {
                publish("=== 证书扫描开始 (增强版) ===");
                publish("目标域名: " + domain);
                publish("自动Cloudflare绕过: " + (autoBypassCheckBox.isSelected() ? "启用" : "禁用"));
                publish("");

                // Step 1: Search certificates
                publish("步骤 1: 查询证书信息...");
                List<CertificateInfo> certificates = certificateService.searchCertificates(domain);

                if (certificates.isEmpty()) {
                    publish("未找到相关证书");
                    return null;
                }

                publish("找到 " + certificates.size() + " 个证书");

                // Step 2: Get certificate details
                for (int i = 0; i < certificates.size(); i++) {
                    CertificateInfo cert = certificates.get(i);
                    publish("");
                    publish("步骤 2." + (i + 1) + ": 获取证书详细信息 (ID: " + cert.getId() + ")...");

                    CertificateInfo detailedCert = certificateService.getCertificateDetails(cert.getId());

                    if (detailedCert.getSha256Fingerprint() != null) {
                        publish("证书ID: " + detailedCert.getId());
                        publish("通用名称: " + detailedCert.getCommonName());
                        publish("SHA-256指纹: " + detailedCert.getSha256Fingerprint());
                        publish("颁发者: " + detailedCert.getIssuer());
                        publish("有效期: " + detailedCert.getValidFrom() + " 至 " + detailedCert.getValidTo());

                        // Step 3: Search domains with same certificate
                        publish("");
                        publish("步骤 3." + (i + 1) + ": 查询使用相同证书的网站...");

                        try {
                            List<String> domains = censysService.searchDomainsByCertFingerprint(
                                detailedCert.getSha256Fingerprint());

                            if (domains.isEmpty()) {
                                publish("未找到使用相同证书的其他网站");
                            } else {
                                publish("找到 " + domains.size() + " 个使用相同证书的网站:");
                                for (String foundDomain : domains) {
                                    publish("  - " + foundDomain);
                                }
                            }
                        } catch (Exception e) {
                            publish("Censys查询失败: " + e.getMessage());
                            if (e.getMessage().contains("Cookie")) {
                                publish("提示: 可以点击'自动获取Cookie'按钮获取Cookie");
                            }
                        }
                    } else {
                        publish("无法获取证书的SHA-256指纹");
                    }
                }

                publish("");
                publish("=== 扫描完成 ===");

            } catch (IOException e) {
                publish("扫描过程中发生错误: " + e.getMessage());
            }

            return null;
        }

        @Override
        protected void done() {
            SwingUtilities.invokeLater(() -> {
                scanButton.setEnabled(true);
                progressBar.setVisible(false);
                statusLabel.setText("扫描完成");
            });
        }
    }

    /**
     * Main method to start the application
     */
    public static void main(String[] args) {
        SwingUtilities.invokeLater(new Runnable() {
            @Override
            public void run() {
                try {
                    // Set system look and feel
                    UIManager.setLookAndFeel(UIManager.getSystemLookAndFeel());
                } catch (Exception e) {
                    // Use default look and feel
                }

                new CertificateScannerGUI().setVisible(true);
            }
        });
    }

    /**
     * Clean up resources when window is closed
     */
    @Override
    public void dispose() {
        try {
            if (certificateService != null) {
                certificateService.close();
            }
            if (censysService != null) {
                censysService.close();
            }
        } catch (IOException e) {
            System.err.println("Error closing services: " + e.getMessage());
        }
        super.dispose();
    }
}
