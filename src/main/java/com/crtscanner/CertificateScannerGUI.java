package com.crtscanner;

import javax.swing.*;
import javax.swing.border.TitledBorder;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.io.IOException;
import java.util.List;


/**
 * Main GUI application for Certificate Scanner
 */
public class CertificateScannerGUI extends JFrame {
    private JTextField domainField;
    private JTextArea cookieArea;
    private JTextArea resultArea;
    private JButton scanButton;
    private JButton clearButton;
    private JProgressBar progressBar;
    private JLabel statusLabel;
    
    private CertificateService certificateService;
    private CensysService censysService;
    
    public CertificateScannerGUI() {
        this.certificateService = new CertificateService();
        this.censysService = new CensysService();
        
        initializeGUI();
        setupEventHandlers();
    }
    
    private void initializeGUI() {
        setTitle("Certificate Scanner - 证书扫描工具");
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        setLayout(new BorderLayout());
        
        // Create main panel
        JPanel mainPanel = new JPanel(new BorderLayout(10, 10));
        mainPanel.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));
        
        // Input panel
        JPanel inputPanel = createInputPanel();
        mainPanel.add(inputPanel, BorderLayout.NORTH);
        
        // Result panel
        JPanel resultPanel = createResultPanel();
        mainPanel.add(resultPanel, BorderLayout.CENTER);
        
        // Status panel
        JPanel statusPanel = createStatusPanel();
        mainPanel.add(statusPanel, BorderLayout.SOUTH);
        
        add(mainPanel);
        
        // Set window properties
        setSize(800, 700);
        setLocationRelativeTo(null);
        setMinimumSize(new Dimension(600, 500));
    }
    
    private JPanel createInputPanel() {
        JPanel panel = new JPanel(new GridBagLayout());
        panel.setBorder(new TitledBorder("输入配置"));
        GridBagConstraints gbc = new GridBagConstraints();
        
        // Domain input
        gbc.gridx = 0; gbc.gridy = 0;
        gbc.anchor = GridBagConstraints.WEST;
        gbc.insets = new Insets(5, 5, 5, 5);
        panel.add(new JLabel("目标域名:"), gbc);
        
        gbc.gridx = 1; gbc.gridy = 0;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        gbc.weightx = 1.0;
        domainField = new JTextField(20);
        domainField.setToolTipText("请输入要查询的域名，例如: example.com");
        panel.add(domainField, gbc);
        
        // Cookie input
        gbc.gridx = 0; gbc.gridy = 1;
        gbc.anchor = GridBagConstraints.NORTHWEST;
        gbc.fill = GridBagConstraints.NONE;
        gbc.weightx = 0;
        panel.add(new JLabel("Censys Cookie:"), gbc);
        
        gbc.gridx = 1; gbc.gridy = 1;
        gbc.fill = GridBagConstraints.BOTH;
        gbc.weightx = 1.0;
        gbc.weighty = 0.3;
        cookieArea = new JTextArea(3, 20);
        cookieArea.setLineWrap(true);
        cookieArea.setWrapStyleWord(true);
        cookieArea.setToolTipText("请输入Censys网站的Cookie用于身份验证");
        JScrollPane cookieScroll = new JScrollPane(cookieArea);
        panel.add(cookieScroll, gbc);
        
        // Buttons
        gbc.gridx = 0; gbc.gridy = 2;
        gbc.gridwidth = 2;
        gbc.fill = GridBagConstraints.NONE;
        gbc.anchor = GridBagConstraints.CENTER;
        gbc.weighty = 0;
        
        JPanel buttonPanel = new JPanel(new FlowLayout());
        scanButton = new JButton("开始扫描");
        scanButton.setPreferredSize(new Dimension(100, 30));
        clearButton = new JButton("清空结果");
        clearButton.setPreferredSize(new Dimension(100, 30));
        
        buttonPanel.add(scanButton);
        buttonPanel.add(clearButton);
        panel.add(buttonPanel, gbc);
        
        return panel;
    }
    
    private JPanel createResultPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBorder(new TitledBorder("扫描结果"));
        
        resultArea = new JTextArea();
        resultArea.setEditable(false);
        resultArea.setFont(new Font(Font.MONOSPACED, Font.PLAIN, 12));
        resultArea.setBackground(Color.WHITE);
        
        JScrollPane scrollPane = new JScrollPane(resultArea);
        scrollPane.setVerticalScrollBarPolicy(JScrollPane.VERTICAL_SCROLLBAR_ALWAYS);
        scrollPane.setHorizontalScrollBarPolicy(JScrollPane.HORIZONTAL_SCROLLBAR_AS_NEEDED);
        
        panel.add(scrollPane, BorderLayout.CENTER);
        
        return panel;
    }
    
    private JPanel createStatusPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        
        statusLabel = new JLabel("就绪");
        statusLabel.setBorder(BorderFactory.createEmptyBorder(5, 5, 5, 5));
        
        progressBar = new JProgressBar();
        progressBar.setStringPainted(true);
        progressBar.setVisible(false);
        
        panel.add(statusLabel, BorderLayout.WEST);
        panel.add(progressBar, BorderLayout.CENTER);
        
        return panel;
    }
    
    private void setupEventHandlers() {
        scanButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                startScan();
            }
        });
        
        clearButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                resultArea.setText("");
                statusLabel.setText("就绪");
            }
        });
        
        // Enter key in domain field triggers scan
        domainField.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                startScan();
            }
        });
    }
    
    private void startScan() {
        String domain = domainField.getText().trim();
        if (domain.isEmpty()) {
            JOptionPane.showMessageDialog(this, "请输入域名", "错误", JOptionPane.ERROR_MESSAGE);
            return;
        }

        String cookie = cookieArea.getText().trim();
        if (cookie.isEmpty()) {
            int result = JOptionPane.showConfirmDialog(this,
                "未设置Censys Cookie，将无法查询使用同证书的网站。是否继续？",
                "警告", JOptionPane.YES_NO_OPTION);
            if (result != JOptionPane.YES_OPTION) {
                return;
            }
        }

        // Start scanning in background thread
        new ScanWorker(domain, cookie).execute();
    }

    /**
     * Background worker for scanning operations
     */
    private class ScanWorker extends SwingWorker<Void, String> {
        private final String domain;
        private final String cookie;

        public ScanWorker(String domain, String cookie) {
            this.domain = domain;
            this.cookie = cookie;
        }

        @Override
        protected void process(List<String> chunks) {
            for (String message : chunks) {
                resultArea.append(message + "\n");
                resultArea.setCaretPosition(resultArea.getDocument().getLength());
            }
        }

        @Override
        protected Void doInBackground() throws Exception {
            SwingUtilities.invokeLater(() -> {
                scanButton.setEnabled(false);
                progressBar.setVisible(true);
                progressBar.setIndeterminate(true);
                statusLabel.setText("正在扫描...");
                resultArea.setText("");
            });

            try {
                publish("=== 证书扫描开始 ===");
                publish("目标域名: " + domain);
                publish("");

                // Step 1: Search certificates
                publish("步骤 1: 查询证书信息...");
                List<CertificateInfo> certificates = certificateService.searchCertificates(domain);

                if (certificates.isEmpty()) {
                    publish("未找到相关证书");
                    return null;
                }

                publish("找到 " + certificates.size() + " 个证书");

                // Step 2: Get certificate details
                for (int i = 0; i < certificates.size(); i++) {
                    CertificateInfo cert = certificates.get(i);
                    publish("");
                    publish("步骤 2." + (i + 1) + ": 获取证书详细信息 (ID: " + cert.getId() + ")...");

                    CertificateInfo detailedCert = certificateService.getCertificateDetails(cert.getId());

                    if (detailedCert.getSha256Fingerprint() != null) {
                        publish("证书ID: " + detailedCert.getId());
                        publish("通用名称: " + detailedCert.getCommonName());
                        publish("SHA-256指纹: " + detailedCert.getSha256Fingerprint());
                        publish("颁发者: " + detailedCert.getIssuer());
                        publish("有效期: " + detailedCert.getValidFrom() + " 至 " + detailedCert.getValidTo());

                        // Step 3: Search domains with same certificate
                        if (!cookie.isEmpty()) {
                            publish("");
                            publish("步骤 3." + (i + 1) + ": 查询使用相同证书的网站...");

                            try {
                                censysService.setCookie(cookie);
                                List<String> domains = censysService.searchDomainsByCertFingerprint(
                                    detailedCert.getSha256Fingerprint());

                                if (domains.isEmpty()) {
                                    publish("未找到使用相同证书的其他网站");
                                } else {
                                    publish("找到 " + domains.size() + " 个使用相同证书的网站:");
                                    for (String foundDomain : domains) {
                                        publish("  - " + foundDomain);
                                    }
                                }
                            } catch (Exception e) {
                                publish("Censys查询失败: " + e.getMessage());
                                publish("请检查Cookie是否正确或网络连接");
                            }
                        }
                    } else {
                        publish("无法获取证书的SHA-256指纹");
                    }
                }

                publish("");
                publish("=== 扫描完成 ===");

            } catch (IOException e) {
                publish("扫描过程中发生错误: " + e.getMessage());
            }

            return null;
        }

        @Override
        protected void done() {
            SwingUtilities.invokeLater(() -> {
                scanButton.setEnabled(true);
                progressBar.setVisible(false);
                statusLabel.setText("扫描完成");
            });
        }
    }

    /**
     * Main method to start the application
     */
    public static void main(String[] args) {
        SwingUtilities.invokeLater(new Runnable() {
            @Override
            public void run() {
                new CertificateScannerGUI().setVisible(true);
            }
        });
    }

    /**
     * Clean up resources when window is closed
     */
    @Override
    public void dispose() {
        try {
            if (certificateService != null) {
                certificateService.close();
            }
            if (censysService != null) {
                censysService.close();
            }
        } catch (IOException e) {
            System.err.println("Error closing services: " + e.getMessage());
        }
        super.dispose();
    }
}
