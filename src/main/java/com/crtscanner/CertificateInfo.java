package com.crtscanner;

/**
 * Certificate information model
 */
public class CertificateInfo {
    private String id;
    private String commonName;
    private String sha256Fingerprint;
    private String issuer;
    private String validFrom;
    private String validTo;
    
    public CertificateInfo() {}
    
    public CertificateInfo(String id, String commonName, String sha256Fingerprint) {
        this.id = id;
        this.commonName = commonName;
        this.sha256Fingerprint = sha256Fingerprint;
    }
    
    // Getters and Setters
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
    
    public String getCommonName() {
        return commonName;
    }
    
    public void setCommonName(String commonName) {
        this.commonName = commonName;
    }
    
    public String getSha256Fingerprint() {
        return sha256Fingerprint;
    }
    
    public void setSha256Fingerprint(String sha256Fingerprint) {
        this.sha256Fingerprint = sha256Fingerprint;
    }
    
    public String getIssuer() {
        return issuer;
    }
    
    public void setIssuer(String issuer) {
        this.issuer = issuer;
    }
    
    public String getValidFrom() {
        return validFrom;
    }
    
    public void setValidFrom(String validFrom) {
        this.validFrom = validFrom;
    }
    
    public String getValidTo() {
        return validTo;
    }
    
    public void setValidTo(String validTo) {
        this.validTo = validTo;
    }
    
    @Override
    public String toString() {
        return "CertificateInfo{" +
                "id='" + id + '\'' +
                ", commonName='" + commonName + '\'' +
                ", sha256Fingerprint='" + sha256Fingerprint + '\'' +
                ", issuer='" + issuer + '\'' +
                ", validFrom='" + validFrom + '\'' +
                ", validTo='" + validTo + '\'' +
                '}';
    }
}
