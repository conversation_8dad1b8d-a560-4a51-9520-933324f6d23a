# 证书扫描工具 (Certificate Scanner)

一个用于自动化获取网站证书并查询使用同证书网站的Java图形化界面程序。

## 功能特性

- 🔍 查询指定域名的SSL证书信息
- 📋 获取证书的SHA-256指纹
- 🌐 通过Censys平台查询使用相同证书的其他网站
- 🖥️ 友好的图形化界面
- 📊 实时显示扫描进度和结果

## 工作流程

1. **证书查询**: 从 `https://crt.sh/?q=域名` 查询目标域名的证书
2. **详细信息获取**: 获取第一个和最后一个证书的详细信息
3. **SHA-256提取**: 从证书详情页面提取SHA-256指纹
4. **同证书网站查询**: 使用Censys平台查询使用相同证书的网站

## 系统要求

- Java 8 或更高版本
- 网络连接
- Censys平台账户（可选，用于查询同证书网站）

## 安装和运行

### 方法1: 直接运行JAR包

```bash
java -jar crt-scanner-1.0.0.jar
```

### 方法2: 从源码编译

```bash
# 克隆项目
git clone <repository-url>
cd crt-scan

# 编译项目
mvn clean compile

# 打包成JAR
mvn package

# 运行程序
java -jar target/crt-scanner-1.0.0.jar
```

## 使用说明

### 基本使用

1. **启动程序**: 双击JAR文件或使用命令行运行
2. **输入域名**: 在"目标域名"字段输入要查询的域名（如：example.com）
3. **开始扫描**: 点击"开始扫描"按钮

### 配置Censys Cookie（可选）

为了查询使用相同证书的其他网站，需要配置Censys平台的Cookie：

1. **注册Censys账户**: 访问 [https://censys.io](https://censys.io) 注册账户
2. **登录并获取Cookie**: 
   - 登录Censys平台
   - 打开浏览器开发者工具（F12）
   - 访问任意搜索页面
   - 在Network标签中找到请求头中的Cookie值
3. **配置Cookie**: 将完整的Cookie字符串粘贴到程序的"Censys Cookie"文本框中

### Cookie获取示例

Cookie格式通常如下：
```
session_id=xxx; csrf_token=xxx; other_cookies=xxx
```

## 界面说明

- **目标域名**: 输入要查询的域名
- **Censys Cookie**: 输入Censys平台的认证Cookie（可选）
- **开始扫描**: 开始执行证书扫描流程
- **清空结果**: 清空结果显示区域
- **扫描结果**: 显示详细的扫描过程和结果

## 输出信息

程序会显示以下信息：

- 证书ID和通用名称
- SHA-256指纹
- 证书颁发者
- 证书有效期
- 使用相同证书的其他网站列表

## 注意事项

1. **网络连接**: 程序需要稳定的网络连接来访问crt.sh和Censys
2. **Cloudflare保护**: Censys可能有Cloudflare保护，需要有效的Cookie
3. **速率限制**: 请合理使用，避免过于频繁的请求
4. **隐私保护**: 请妥善保管您的Censys Cookie信息

## 故障排除

### 常见问题

1. **"未找到相关证书"**: 
   - 检查域名是否正确
   - 确认域名是否有SSL证书

2. **"Censys查询失败"**:
   - 检查Cookie是否正确
   - 确认网络连接正常
   - 可能遇到Cloudflare保护

3. **程序无法启动**:
   - 确认Java版本（需要Java 8+）
   - 检查JAR文件是否完整

### 日志信息

程序会在控制台输出详细的日志信息，有助于诊断问题。

## 技术栈

- **Java 8**: 核心开发语言
- **Swing**: 图形用户界面
- **Apache HttpClient**: HTTP请求处理
- **Jackson**: JSON数据解析
- **Jsoup**: HTML解析
- **Maven**: 项目构建和依赖管理

## 开发者信息

本项目使用Maven构建，主要依赖包括：
- Apache HttpComponents (HTTP客户端)
- Jackson (JSON处理)
- Jsoup (HTML解析)
- SLF4J (日志框架)

## 许可证

本项目仅供学习和研究使用，请遵守相关网站的使用条款。

## 更新日志

### v1.0.0
- 初始版本发布
- 实现基本的证书查询功能
- 支持Censys平台集成
- 提供图形化用户界面
