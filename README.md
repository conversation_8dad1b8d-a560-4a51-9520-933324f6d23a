# 证书扫描工具 (CRT-Scan)

一个自动化获取网站证书并查询使用同证书网站的Python工具，使用curl_cffi绕过可能存在的人机认证。

## 功能特性

- 🔍 自动查询目标域名的SSL证书信息
- 🔐 获取证书SHA-256指纹
- 🌐 搜索使用相同证书的其他网站
- 🛡️ 使用curl_cffi绕过Cloudflare等反爬虫保护
- 📊 支持结果导出到文件

## 工作流程

1. **查询证书信息**: 通过crt.sh查询目标域名的证书记录
2. **获取证书指纹**: 提取第一个和最后一个证书的SHA-256指纹
3. **搜索相关域名**: 在Censys平台搜索使用相同证书的域名
4. **输出结果**: 显示所有使用相同证书的域名列表

## 快速安装

### 自动安装（推荐）

```bash
chmod +x install.sh
./install.sh
```

### 手动安装

```bash
pip install -r requirements.txt
cp config_example.py config.py
# 编辑config.py文件，填入你的Censys cookie
```

## 使用方法

### 基本用法

```bash
python cert_scanner.py example.com
```

### 使用Censys Cookie（推荐）

由于Censys平台需要登录才能使用，建议提供cookie：

```bash
python cert_scanner.py example.com --cookie "your_censys_cookie_here"
```

### 保存结果到文件

```bash
python cert_scanner.py example.com --cookie "your_cookie" --output results.txt
```

### 批量扫描

```bash
# 从文件读取域名列表进行批量扫描
python batch_scanner.py domains_example.txt --cookie "your_cookie" --output batch_results.txt

# JSON格式输出
python batch_scanner.py domains_example.txt --format json --output results.json
```

## 获取Censys Cookie

1. 访问 [Censys平台](https://platform.censys.io/)
2. 登录你的账户
3. 打开浏览器开发者工具 (F12)
4. 切换到Network标签页
5. 刷新页面
6. 找到任意请求，复制Cookie头的值
7. 将Cookie值作为--cookie参数传入

## 参数说明

- `domain`: 目标域名（必需）
- `--cookie`: Censys平台的cookie字符串（可选但推荐）
- `--output, -o`: 输出结果文件路径（可选）

## 示例输出

```
============================================================
证书扫描工具
============================================================
[+] 正在查询 example.com 的证书信息...
[+] 找到证书ID: 第一个=12345678, 最后一个=87654321
[+] 正在获取证书ID 12345678 的SHA-256指纹...
[+] 找到SHA-256指纹: A1B2C3D4E5F6...
[+] 正在Censys平台搜索使用证书 A1B2C3D4E5F6... 的域名...
[+] 找到 5 个使用相同证书的域名

============================================================
扫描结果:
============================================================

找到 5 个使用相同证书的域名:
  1. example.com
  2. www.example.com
  3. api.example.com
  4. cdn.example.com
  5. mail.example.com

扫描完成!
```

## 注意事项

1. **Censys访问限制**: Censys平台需要登录才能使用，建议提供有效的cookie
2. **反爬虫保护**: 工具使用curl_cffi模拟真实浏览器请求，但仍可能遇到保护机制
3. **请求频率**: 建议适当控制请求频率，避免被目标网站封禁
4. **网络环境**: 确保网络连接稳定，某些网站可能需要特定的网络环境

## 故障排除

### 常见问题

1. **403 Forbidden错误**: 通常是因为缺少有效的Censys cookie或遇到反爬虫保护
2. **未找到证书信息**: 检查域名是否正确，或该域名可能没有公开的SSL证书
3. **网络超时**: 检查网络连接，或尝试增加超时时间

### 调试建议

- 使用有效的Censys账户cookie
- 确保目标域名可以正常访问
- 检查网络连接和防火墙设置

## 免责声明

本工具仅用于合法的安全研究和网络管理目的。使用者应遵守相关法律法规和网站服务条款，不得用于非法用途。作者不承担因误用本工具而产生的任何责任。
