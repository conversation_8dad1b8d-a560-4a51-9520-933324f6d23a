import com.crtscanner.CloudflareBypassService;
import com.crtscanner.CensysService;

public class TestCloudflareAPI {
    public static void main(String[] args) {
        System.out.println("🔍 Certificate Scanner 增强版 - Cloudflare绕过API测试");
        System.out.println("=" * 60);
        
        // 测试Cloudflare绕过服务
        CloudflareBypassService cloudflareService = new CloudflareBypassService();
        
        try {
            System.out.println("\n📋 测试1: API连接测试");
            boolean connectionTest = cloudflareService.testConnection();
            System.out.println("连接测试结果: " + (connectionTest ? "✅ 成功" : "❌ 失败"));
            
            System.out.println("\n📋 测试2: 获取示例域名Cookie");
            String exampleCookie = cloudflareService.getCookie("example.com");
            if (exampleCookie != null) {
                System.out.println("✅ 成功获取Cookie: " + exampleCookie.substring(0, Math.min(50, exampleCookie.length())) + "...");
            } else {
                System.out.println("❌ 获取Cookie失败");
            }
            
            System.out.println("\n📋 测试3: 获取Censys Cookie");
            String censysCookie = cloudflareService.getCensysCookie();
            if (censysCookie != null) {
                System.out.println("✅ 成功获取Censys Cookie: " + censysCookie.substring(0, Math.min(50, censysCookie.length())) + "...");
            } else {
                System.out.println("❌ 获取Censys Cookie失败");
            }
            
            // 测试Censys服务
            System.out.println("\n📋 测试4: Censys服务自动绕过功能");
            CensysService censysService = new CensysService();
            
            boolean autoGetResult = censysService.autoGetCensysCookie();
            System.out.println("自动获取Cookie结果: " + (autoGetResult ? "✅ 成功" : "❌ 失败"));
            
            System.out.println("\n🎉 测试完成!");
            System.out.println("\n💡 使用说明:");
            System.out.println("1. 运行主程序: java -jar target/crt-scanner-2.0.0.jar");
            System.out.println("2. 点击'测试API'按钮验证API连接");
            System.out.println("3. 点击'自动获取Cookie'按钮获取Censys Cookie");
            System.out.println("4. 启用'自动绕过Cloudflare保护'选项");
            System.out.println("5. 输入域名开始扫描");
            
        } catch (Exception e) {
            System.err.println("❌ 测试过程中发生错误: " + e.getMessage());
            e.printStackTrace();
        } finally {
            try {
                cloudflareService.close();
            } catch (Exception e) {
                System.err.println("关闭服务时发生错误: " + e.getMessage());
            }
        }
    }
}
