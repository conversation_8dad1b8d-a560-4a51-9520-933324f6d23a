#!/bin/bash

# Certificate Scanner 启动脚本
# 
# 使用方法:
#   ./run.sh          # 直接运行程序
#   ./run.sh build    # 重新编译并运行
#   ./run.sh clean    # 清理编译文件

case "$1" in
    "build")
        echo "正在编译项目..."
        mvn clean package
        if [ $? -eq 0 ]; then
            echo "编译成功，启动程序..."
            java -jar target/crt-scanner-1.0.0.jar
        else
            echo "编译失败！"
            exit 1
        fi
        ;;
    "clean")
        echo "清理编译文件..."
        mvn clean
        echo "清理完成！"
        ;;
    *)
        echo "启动证书扫描工具..."
        if [ -f "target/crt-scanner-1.0.0.jar" ]; then
            java -jar target/crt-scanner-1.0.0.jar
        else
            echo "JAR文件不存在，正在编译..."
            mvn clean package
            if [ $? -eq 0 ]; then
                echo "编译成功，启动程序..."
                java -jar target/crt-scanner-1.0.0.jar
            else
                echo "编译失败！"
                exit 1
            fi
        fi
        ;;
esac
