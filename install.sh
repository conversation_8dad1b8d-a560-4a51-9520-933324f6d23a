#!/bin/bash

# 证书扫描工具安装脚本

echo "=================================="
echo "证书扫描工具 (CRT-Scan) 安装脚本"
echo "=================================="

# 检查Python版本
echo "检查Python版本..."
python3 --version
if [ $? -ne 0 ]; then
    echo "错误: 未找到Python3，请先安装Python3"
    exit 1
fi

# 检查pip
echo "检查pip..."
python3 -m pip --version
if [ $? -ne 0 ]; then
    echo "错误: 未找到pip，请先安装pip"
    exit 1
fi

# 创建虚拟环境（可选）
read -p "是否创建虚拟环境? (y/n): " create_venv
if [ "$create_venv" = "y" ] || [ "$create_venv" = "Y" ]; then
    echo "创建虚拟环境..."
    python3 -m venv venv
    source venv/bin/activate
    echo "虚拟环境已激活"
fi

# 安装依赖
echo "安装依赖包..."
python3 -m pip install -r requirements.txt

if [ $? -eq 0 ]; then
    echo "依赖安装成功!"
else
    echo "依赖安装失败，请检查网络连接和pip配置"
    exit 1
fi

# 创建配置文件
if [ ! -f "config.py" ]; then
    echo "创建配置文件..."
    cp config_example.py config.py
    echo "配置文件已创建: config.py"
    echo "请编辑config.py文件，填入你的Censys cookie"
fi

# 创建结果目录
mkdir -p results
echo "结果目录已创建: results/"

# 设置执行权限
chmod +x cert_scanner.py
chmod +x batch_scanner.py

echo ""
echo "=================================="
echo "安装完成!"
echo "=================================="
echo ""
echo "使用方法:"
echo "1. 单个域名扫描:"
echo "   python3 cert_scanner.py example.com"
echo ""
echo "2. 批量扫描:"
echo "   python3 batch_scanner.py domains_example.txt"
echo ""
echo "3. 使用cookie:"
echo "   python3 cert_scanner.py example.com --cookie 'your_cookie_here'"
echo ""
echo "注意: 请先编辑config.py文件，填入你的Censys平台cookie"
echo ""
