#!/usr/bin/env python3
"""
配置文件示例
复制此文件为config.py并填入你的配置信息
"""

# Censys平台配置
CENSYS_COOKIE = "your_censys_cookie_here"

# 请求配置
REQUEST_TIMEOUT = 30  # 请求超时时间（秒）
REQUEST_DELAY = 1     # 请求间隔时间（秒）

# 输出配置
DEFAULT_OUTPUT_DIR = "results"  # 默认输出目录
VERBOSE = True                  # 是否显示详细信息

# User-Agent配置
USER_AGENTS = [
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 Safari/537.36',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 Safari/537.36',
    'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 Safari/537.36'
]
