# Certificate Scanner Java增强版 v2.0.0

一个用于自动化获取网站证书并查询使用同证书网站的Java图形化界面程序，集成了Cloudflare绕过功能。

## 🆕 增强版特性

- 🔍 查询指定域名的SSL证书信息
- 📋 获取证书的SHA-256指纹
- 🌐 通过Censys平台查询使用相同证书的其他网站
- 🛡️ **集成Cloudflare绕过API，自动获取Cookie**
- 🖥️ 友好的图形化界面
- 🤖 一键自动获取Censys Cookie
- 📊 实时显示扫描进度和结果
- ⚡ 智能重试机制
- 🔄 自动错误恢复

## 工作流程

1. **证书查询**: 从 `https://crt.sh/?q=域名` 查询目标域名的证书
2. **详细信息获取**: 获取第一个和最后一个证书的详细信息
3. **SHA-256提取**: 从证书详情页面提取SHA-256指纹
4. **自动获取Cookie**: 使用API自动获取Censys平台Cookie
5. **同证书网站查询**: 使用获取的Cookie查询使用相同证书的网站

## 系统要求

- Java 8 或更高版本
- 网络连接
- Maven 3.6+ (用于编译)

## 安装和运行

### 方法1: 直接运行JAR包

```bash
java -jar target/crt-scanner-2.0.0.jar
```

### 方法2: 从源码编译

```bash
# 编译项目
mvn clean compile

# 打包成JAR
mvn package

# 运行程序
java -jar target/crt-scanner-2.0.0.jar
```

## 使用说明

### 基本使用

1. **启动程序**: 双击JAR文件或使用命令行运行
2. **输入域名**: 在"目标域名"字段输入要查询的域名（如：example.com）
3. **启用自动绕过**: 确保"自动绕过Cloudflare保护"选项已勾选
4. **开始扫描**: 点击"开始扫描"按钮

### 高级功能

#### 自动获取Cookie
- 点击"自动获取Cookie"按钮，程序会自动从API获取Censys平台的Cookie
- 无需手动复制粘贴Cookie

#### API测试
- 点击"测试API"按钮验证Cloudflare绕过API是否正常工作
- 确保网络连接和API服务可用

#### 手动设置Cookie
- 如果自动获取失败，可以在"Censys Cookie"文本框中手动输入Cookie
- Cookie格式：`session_id=xxx; csrf_token=xxx; other_cookies=xxx`

## 界面说明

- **目标域名**: 输入要查询的域名
- **自动绕过Cloudflare保护**: 启用后自动获取Cookie绕过保护
- **Censys Cookie**: 显示当前使用的Cookie（自动获取或手动输入）
- **开始扫描**: 开始执行证书扫描流程
- **清空结果**: 清空结果显示区域
- **自动获取Cookie**: 一键获取Censys Cookie
- **测试API**: 测试Cloudflare绕过API连接
- **扫描结果**: 显示详细的扫描过程和结果

## 输出信息

程序会显示以下信息：

- 证书ID和通用名称
- SHA-256指纹
- 证书颁发者
- 证书有效期
- 使用相同证书的其他网站列表
- 自动绕过状态和Cookie获取结果

## 技术特点

### Cloudflare绕过
- 使用第三方API自动获取Cookie
- 智能检测Cloudflare拦截
- 自动重试机制
- 支持多种域名的Cookie获取

### 错误处理
- 完善的异常处理机制
- 用户友好的错误提示
- 自动恢复和重试
- 详细的日志输出

### 界面优化
- 现代化的GUI设计
- 实时进度显示
- 彩色按钮和状态指示
- 响应式布局

## API配置

程序使用以下API进行Cloudflare绕过：

- **API地址**: `http://api-cf.zjdanli.com/cloudflare/getCookie`
- **APP ID**: `71gcowhtptnexn8c3dqx0kphsnt7snpv`
- **请求方式**: POST
- **请求格式**: JSON

## 注意事项

1. **网络连接**: 程序需要稳定的网络连接来访问crt.sh、Censys和绕过API
2. **API限制**: 绕过API可能有使用限制，请合理使用
3. **速率控制**: 程序内置了请求间隔，避免过于频繁的请求
4. **隐私保护**: Cookie信息仅用于查询，不会被存储或传输到其他地方

## 故障排除

### 常见问题

1. **"API连接测试失败"**:
   - 检查网络连接
   - 确认API服务是否可用
   - 尝试稍后重试

2. **"Cookie获取失败"**:
   - 检查目标域名是否正确
   - 确认API服务状态
   - 可以尝试手动设置Cookie

3. **"Censys查询失败"**:
   - 确认Cookie是否有效
   - 检查网络连接
   - 尝试重新获取Cookie

4. **程序无法启动**:
   - 确认Java版本（需要Java 8+）
   - 检查JAR文件是否完整
   - 查看控制台错误信息

## 更新日志

### v2.0.0 (增强版)
- ✨ 新增Cloudflare绕过API集成
- ✨ 新增自动获取Cookie功能
- ✨ 新增API连接测试功能
- ✨ 改进用户界面设计
- ✨ 增强错误处理和重试机制
- ✨ 优化扫描流程和用户体验

### v1.0.0 (基础版)
- 🎉 初始版本发布
- 📋 基本证书查询功能
- 🌐 Censys平台集成
- 🖥️ 图形化用户界面

## 技术栈

- **Java 8**: 核心开发语言
- **Swing**: 图形用户界面
- **Apache HttpClient**: HTTP请求处理
- **Jackson**: JSON数据解析
- **Jsoup**: HTML解析
- **Maven**: 项目构建和依赖管理

## 许可证

本项目仅供学习和研究使用，请遵守相关网站的使用条款和API服务条款。

---

🎉 **Certificate Scanner Java增强版开发完成！**

现在您可以享受自动化的Cloudflare绕过功能，无需手动获取Cookie即可完成完整的证书扫描流程。
