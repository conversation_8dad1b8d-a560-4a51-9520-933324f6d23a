import com.crtscanner.CertificateService;
import com.crtscanner.CertificateInfo;
import java.util.List;

public class TestCertificateService {
    public static void main(String[] args) {
        CertificateService service = new CertificateService();
        
        try {
            System.out.println("Testing certificate search for example.com...");
            List<CertificateInfo> certificates = service.searchCertificates("example.com");
            
            if (certificates.isEmpty()) {
                System.out.println("No certificates found");
            } else {
                System.out.println("Found " + certificates.size() + " certificates:");
                for (CertificateInfo cert : certificates) {
                    System.out.println("Certificate ID: " + cert.getId());
                    System.out.println("Common Name: " + cert.getCommonName());
                    
                    // Get detailed information
                    if (cert.getId() != null) {
                        System.out.println("Getting details for certificate " + cert.getId() + "...");
                        CertificateInfo detailed = service.getCertificateDetails(cert.getId());
                        System.out.println("SHA-256: " + detailed.getSha256Fingerprint());
                        System.out.println("Issuer: " + detailed.getIssuer());
                        System.out.println("---");
                    }
                }
            }
            
            service.close();
            System.out.println("Test completed successfully!");
            
        } catch (Exception e) {
            System.err.println("Test failed: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
